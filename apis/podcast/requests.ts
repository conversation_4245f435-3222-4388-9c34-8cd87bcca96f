import { env } from '@/utils/const';
import { request } from '../axios';
import {
  IAddFavoritePodcastBulkRequest,
  IAddPodcastRateParams,
  IDeletePodcastRateParams,
  IGetMarkAsWatchRequest,
  IGetNewestEpisodesResponse,
  IGetPodcastByIdParams,
  IGetPodcastByIdResponse,
  IGetPodcastListParams,
  IGetPodcastRateByPodcastIdParams,
  IGetPodcastRateListParams,
  IGetPodcastRateStatsResponse,
  IGetPodcastRatesResponse,
  IGetPodcastsResponse,
  IGetPodcastsSearchInfiniteResponse,
  IGetWatchListRequest,
  IMarkAsWatchRequest,
  IMarkAsWatchResponse,
  IPodcastRateResponse,
  IUpdatePodcastRateParams,
  IWatchlistAddRequest,
  IWatchlistResponse,
  IGetCommunityFeedResponse,
} from './types';
import { Image } from 'expo-image';

export const getPodcastListRequest = async (params: IGetPodcastListParams): Promise<IGetPodcastsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast`,
    method: 'GET',
    params,
  });

  return data;
};

export const getPodcastListSearchInfiniteRequest = async (
  params: IGetPodcastListParams
): Promise<IGetPodcastsSearchInfiniteResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast/search`,
    method: 'GET',
    params,
  });

  return data;
};

export const getPodcastRateListRequest = async (
  params: IGetPodcastRateListParams
): Promise<IGetPodcastRatesResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-rate/list/${params.podcastId}`,
    method: 'GET',
    params,
  });

  return data;
};

export const getPodcastRateStatsRequest = async (podcastId: string): Promise<IGetPodcastRateStatsResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-rate/stats/${podcastId}`,
    method: 'GET',
  });

  return data.data;
};

export const getPodcastByIdRequest = async (params: IGetPodcastByIdParams): Promise<IGetPodcastByIdResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast/${params.podcastId}`,
    method: 'GET',
  });

  return data.data;
};

export const getEpisodesListRequest = async (params: IGetPodcastListParams): Promise<IGetNewestEpisodesResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/episode`,
    method: 'GET',
    params,
  });

  return data;
};

export const getPodcastRateByPodcastIdRequest = async ({
  podcastId,
}: IGetPodcastRateByPodcastIdParams): Promise<IPodcastRateResponse> => {
  const { data } = await request({ url: `${env.API_VERSION}/podcast-rate/${podcastId}`, method: 'GET' });
  return data.data;
};

export const addPodcastRateRequest = async (params: IAddPodcastRateParams): Promise<IPodcastRateResponse> => {
  const { data } = await request({ url: `${env.API_VERSION}/podcast-rate`, method: 'POST', data: params });
  return data.data;
};

export const updatePodcastRateRequest = async (params: IUpdatePodcastRateParams): Promise<IPodcastRateResponse> => {
  const { id, ...requestData } = params;
  const { data } = await request({
    url: `${env.API_VERSION}/podcast-rate/${id}`,
    method: 'PATCH',
    data: requestData,
  });
  return data.data;
};

export const deletePodcastRateRequest = async (params: IDeletePodcastRateParams): Promise<void> => {
  await request({ url: `${env.API_VERSION}/podcast-rate/${params.id}`, method: 'DELETE' });
};

// Watchlist operations
export const getWatchlistRequest = async ({ podcastId }: IGetWatchListRequest): Promise<IWatchlistResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/watch-list/${podcastId}`,
    method: 'GET',
  });
  return data.data;
};

export const addToWatchlistRequest = async (params: IWatchlistAddRequest): Promise<IWatchlistResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/watch-list/add`,
    method: 'POST',
    data: params,
  });

  return data;
};

export const removeFromWatchlistRequest = async (id: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/watch-list/${id}`,
    method: 'DELETE',
  });
};

// Mark as watch operations
export const getMarkAsWatchedRequest = async ({ podcastId }: IGetMarkAsWatchRequest): Promise<IMarkAsWatchResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/mark-as-watched/${podcastId}`,
    method: 'GET',
  });
  return data.data;
};

export const markAsWatchedRequest = async (params: IMarkAsWatchRequest): Promise<IMarkAsWatchResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/mark-as-watched/add`,
    method: 'POST',
    data: params,
  });

  return data;
};

export const removeMarkAsWatchedRequest = async (id: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/mark-as-watched/${id}`,
    method: 'DELETE',
  });
};

export const likePodcastRequest = async (podcastId: string): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/podcast/${podcastId}/like`,
    method: 'POST',
  });
};

export const addFavoritePodcastBulkRequest = async (params: IAddFavoritePodcastBulkRequest): Promise<void> => {
  await request({
    url: `${env.API_VERSION}/favorite-podcast/bulk`,
    method: 'POST',
    data: params.podcastIds,
  });
};

export const getCommunityFeedRequest = async (params: IGetPodcastListParams): Promise<IGetCommunityFeedResponse> => {
  const { data } = await request({
    url: `${env.API_VERSION}/podcast/community-feed`,
    method: 'GET',
    params,
  });

  const feedList = data.data as IGetCommunityFeedResponse['data'];

  feedList.map((feed) => {
    const postCommentImages = feed?.images;
    const posterAvatar = feed.user?.avatar;
    const postImage = feed?.parentImageUrl;

    Image.prefetch(postCommentImages);
    Image.prefetch(posterAvatar);
    Image.prefetch(postImage);
  });

  return data;
};
