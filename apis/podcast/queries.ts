import queryKeys from '@/utils/queryKeys';
import { UseQueryOptions, useInfiniteQuery, useQuery } from '@tanstack/react-query';
import { AxiosError } from 'axios';
import {
  getEpisodesListRequest,
  getMarkAsWatchedRequest,
  getPodcastByIdRequest,
  getPodcastListRequest,
  getPodcastListSearchInfiniteRequest,
  getPodcastRateByPodcastIdRequest,
  getPodcastRateListRequest,
  getPodcastRateStatsRequest,
  getWatchlistRequest,
  getCommunityFeedRequest,
} from './requests';
import {
  IGetMarkAsWatchRequest,
  IGetNewestEpisodesResponse,
  IGetPodcastByIdParams,
  IGetPodcastByIdResponse,
  IGetPodcastListParams,
  IGetPodcastParams,
  IGetPodcastRateByPodcastIdParams,
  IGetPodcastRateListParams,
  IGetPodcastRateStatsResponse,
  IGetPodcastsResponse,
  IGetWatchListRequest,
  IMarkAsWatchResponse,
  IPodcastRateResponse,
  IWatchlistResponse,
} from './types';

export const useGetPodcastsListQuery = (
  params: IGetPodcastListParams,
  options?: UseQueryOptions<IGetPodcastsResponse, AxiosError, IGetPodcastsResponse>
) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.podcasts.podcasts(params),
    queryFn: () => getPodcastListRequest(params),
    ...options,
  });
};

export const useGetNewestEpisodesQuery = (
  params: IGetPodcastListParams,
  options?: UseQueryOptions<IGetNewestEpisodesResponse, AxiosError, IGetNewestEpisodesResponse>
) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.podcasts.newestEpisodes(params),
    queryFn: () => getEpisodesListRequest(params),
    ...options,
  });
};

export const useGetPodcastsInfiniteQuery = (params: IGetPodcastParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    refetchOnMount: true,
    queryKey: queryKeys.podcasts.podcastsInfinite(params),
    queryFn: ({ pageParam }) => getPodcastListRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.totalPages > lastPage.meta.currentPage ? lastPage.meta.currentPage + 1 : undefined;
    },
  });
};

export const useGetPodcastsSearchInfiniteQuery = (params: IGetPodcastParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    refetchOnMount: true,
    queryKey: queryKeys.podcasts.podcastsInfiniteSearch(params),
    enabled: !!params.search,
    queryFn: ({ pageParam }) => getPodcastListSearchInfiniteRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.totalPages > lastPage.meta.currentPage ? lastPage.meta.currentPage + 1 : undefined;
    },
    getPreviousPageParam: (firstPage) => {
      return firstPage.meta.currentPage > 1 ? firstPage.meta.currentPage - 1 : undefined;
    },
  });
};

export const useGetPodcastRateListQuery = (params: IGetPodcastRateListParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    refetchOnMount: true,
    queryKey: queryKeys.rates.list(params),
    queryFn: ({ pageParam }) => getPodcastRateListRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.totalPages > lastPage.meta.currentPage ? lastPage.meta.currentPage + 1 : undefined;
    },
  });
};

export const useGetPodcastByIdQuery = (
  params: IGetPodcastByIdParams,
  options?: Omit<UseQueryOptions<IGetPodcastByIdResponse, AxiosError, IGetPodcastByIdResponse>, 'queryKey'>
) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.podcasts.item(params.podcastId),
    queryFn: () => getPodcastByIdRequest(params),
    ...options,
  });
};

export const useGetPodcastRateByPodcastIdQuery = (
  params: IGetPodcastRateByPodcastIdParams,
  options?: Omit<UseQueryOptions<IPodcastRateResponse, AxiosError, IPodcastRateResponse>, 'queryKey'>
) => {
  return useQuery({
    queryKey: queryKeys.rates.byPodcastId(params.podcastId),
    refetchOnMount: true,
    queryFn: () => getPodcastRateByPodcastIdRequest(params),
    ...options,
  });
};

export const useGetMarkAsWatchQuery = (
  params: IGetMarkAsWatchRequest,
  options?: Omit<UseQueryOptions<IMarkAsWatchResponse, AxiosError, IMarkAsWatchResponse>, 'queryKey'>
) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.markAsWatched.byPodcastId([params.podcastId]),
    queryFn: () => getMarkAsWatchedRequest(params),
    ...options,
  });
};

export const useGetWatchListQuery = (
  params: IGetWatchListRequest,
  options?: Omit<UseQueryOptions<IWatchlistResponse, AxiosError, IWatchlistResponse>, 'queryKey'>
) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.watchlist.byPodcastId([params.podcastId]),
    queryFn: () => getWatchlistRequest(params),
    ...options,
  });
};

export const useGetPodcastRateStatsQuery = (
  podcastId: string,
  options?: Omit<UseQueryOptions<any, AxiosError, IGetPodcastRateStatsResponse>, 'queryKey'>
) => {
  return useQuery({
    refetchOnMount: true,
    queryKey: queryKeys.rates.stat([podcastId]),
    queryFn: () => getPodcastRateStatsRequest(podcastId),
    ...options,
  });
};

export const useGetCommunityFeedQuery = (params: IGetPodcastListParams) => {
  return useInfiniteQuery({
    initialPageParam: 1,
    refetchOnMount: true,
    queryKey: queryKeys.podcasts.communityFeed(params),
    queryFn: ({ pageParam }) => getCommunityFeedRequest({ ...params, page: pageParam }),
    getNextPageParam: (lastPage) => {
      return lastPage.meta.totalPages > lastPage.meta.currentPage ? lastPage.meta.currentPage + 1 : undefined;
    },
  });
};
