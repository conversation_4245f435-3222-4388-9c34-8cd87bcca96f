import { Stack } from 'expo-router';
import * as SplashScreen from 'expo-splash-screen';
import { StatusBar } from 'expo-status-bar';
import { useEffect } from 'react';
import 'react-native-reanimated';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import Providers from './providers';

import { useFonts } from '@expo-google-fonts/inter/useFonts';
import { Inter_400Regular } from '@expo-google-fonts/inter/400Regular';
import { Inter_500Medium } from '@expo-google-fonts/inter/500Medium';
import { Inter_600SemiBold } from '@expo-google-fonts/inter/600SemiBold';
import { Inter_700Bold } from '@expo-google-fonts/inter/700Bold';
import { Inter_800ExtraBold } from '@expo-google-fonts/inter/800ExtraBold';
import { Inter_900Black } from '@expo-google-fonts/inter/900Black';

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export const unstable_settings = {
  initialRouteName: '(app)',
};

export default function RootLayout() {
  const { styles } = useStyles(stylesheet);

  const [loaded] = useFonts({
    Inter: Inter_400Regular,
    InterMedium: Inter_500Medium,
    InterSemiBold: Inter_600SemiBold,
    InterBold: Inter_700Bold,
    InterExtraBold: Inter_800ExtraBold,
    InterBlack: Inter_900Black,
  });

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return (
    <Providers>
      <StatusBar style='light' />

      <Stack screenOptions={{ contentStyle: styles.container }}>
        <Stack.Screen name='(app)' options={{ headerShown: false }} />

        <Stack.Screen name='onboarding' options={{ headerShown: false }} />

        <Stack.Screen name='sign-in' options={{ headerShown: false }} />

        <Stack.Screen name='sign-in-with-phone' options={{ headerShown: false }} />

        <Stack.Screen name='sign-in-with-email' options={{ headerShown: false }} />

        <Stack.Screen name='forgot-password' options={{ headerShown: false }} />

        <Stack.Screen name='reset-password' options={{ headerShown: false }} />

        <Stack.Screen name='reset-password-success' options={{ headerShown: false }} />

        <Stack.Screen name='sign-up' options={{ headerShown: false }} />

        {/* <Stack.Screen name='verify-code' options={{ headerShown: false }} /> */}

        <Stack.Screen name='verify-code-forgot-password' options={{ headerShown: false }} />

        <Stack.Screen name='verify-email-profile' options={{ headerShown: false }} />

        {/* <Stack.Screen name='create-password' options={{ headerShown: false }} /> */}

        <Stack.Screen
          name='(modal)/images'
          options={{
            presentation: 'fullScreenModal',
            title: '',
            headerShown: false,
            animation: 'fade',
          }}
        />

        <Stack.Screen name='+not-found' />
      </Stack>
    </Providers>
  );
}

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
  },
}));
