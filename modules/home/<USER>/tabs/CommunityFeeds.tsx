import { Icons } from '@/assets/icons';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import { memo, useCallback, useEffect, useMemo } from 'react';
import { View, TouchableOpacity } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useRouter } from 'expo-router';
import { useGetCommunityFeedQuery } from '@/apis/podcast';
import { ICommunityFeedItem } from '@/apis/podcast/types';
import { getRelativeTimeString } from '@/utils/dateUtils';
// import { useExpandableText } from '@/hooks/useExpandableText';
import { IconLoading } from '@/components/IconLoading';
import { UserProfileTouch } from '@/components/UserProfileTouch';
import CommentImage from '@/modules/show-detail/components/CommentImage';
import { Avatar } from '@/components/ui/Avatar';
import { useIsYou } from '@/hooks/useIsYou';
import { RateStar } from '@/components/RateStar';
import { useQueryClient } from '@tanstack/react-query';
import { Empty } from '@/components/Empty';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { ListRenderItem } from '@shopify/flash-list';
import queryKeys from '@/utils/queryKeys';

const CommunityFeedCard = memo(({ item }: { item: ICommunityFeedItem }) => {
  const { styles, theme } = useStyles(feedCardStylesheet);
  const router = useRouter();
  const isYou = useIsYou({
    userId: item.user.id,
  });

  // const { isExpanded, showButton, toggleExpanded, onTextLayout, numberOfLines } = useExpandableText({
  //   maxLines: 3,
  // });

  const handleCardPress = useCallback(() => {
    if (item.type === 'podcast') {
      router.push({
        pathname: '/(app)/podcast/[podcastId]/review/[postId]',
        params: {
          podcastId: item.parentId,
          postId: item.commentId,
          source: item.source,
        },
      });
    } else if (item.type === 'episode') {
      router.push({
        pathname: '/(app)/episode/[episodeId]/review/[postId]',
        params: {
          episodeId: item.parentId,
          postId: item.commentId,
          source: item.source,
        },
      });
    }
  }, [item.type, item.parentId, item.commentId, router, item.source]);

  const { primaryTitle, secondaryTitle } = useMemo(() => {
    if (item.type === 'episode') {
      return {
        primaryTitle: item.parentTitle,
        secondaryTitle: item.podcastTitle,
      };
    } else {
      return {
        primaryTitle: item.parentTitle,
        secondaryTitle: '',
      };
    }
  }, [item.type, item.parentTitle, item.podcastTitle]);

  return (
    <View style={styles.cardContainer}>
      <TouchableOpacity onPress={handleCardPress} activeOpacity={0.7}>
        {/* Episode Info Header */}
        <View style={styles.episodeHeader}>
          <ExpoImage source={{ uri: item.parentImageUrl }} style={styles.episodeCover} />

          <View style={styles.episodeInfo}>
            <ThemedText style={styles.episodeTitle} numberOfLines={2} type='defaultMedium'>
              {primaryTitle}
            </ThemedText>

            {secondaryTitle && (
              <ThemedText style={styles.showTitle} numberOfLines={1} type='small'>
                {secondaryTitle}
              </ThemedText>
            )}

            <Spacer height={12} />

            <View style={styles.starsContainer}>
              {item.userRate ? (
                <RateStar rating={item.userRate || 0} size={16} gap={4} maxStars={Math.ceil(item.userRate)} />
              ) : null}

              {item.hasLiked && <Icons.HeartLikeFill size={16} color={'#FF4287'} />}
            </View>
          </View>

          <ThemedText type='tinyMedium' style={styles.duration}>
            {getRelativeTimeString(item.createdAt)}
          </ThemedText>
        </View>

        <Spacer height={20} />

        <View style={styles.reviewSection}>
          {item.commentTitle && (
            <>
              <ThemedText style={styles.reviewTitle} type='defaultMedium'>
                {item.commentTitle}
              </ThemedText>
              <Spacer height={8} />
            </>
          )}

          <ThemedText style={styles.reviewContent} type='small'>
            {item.commentContent?.trim()}
          </ThemedText>
        </View>

        {(item?.images?.length || 0) > 0 && <Spacer height={20} />}

        {(item?.images?.length || 0) > 0 && (
          <View style={item.images?.length > 1 ? { marginRight: -24 } : {}}>
            <CommentImage images={item.images} />
          </View>
        )}

        <Spacer height={20} />
      </TouchableOpacity>

      <UserProfileTouch userId={item.user.id} userType={item.source} style={styles.userSection}>
        <View style={styles.userSection}>
          <Avatar image={item.user.avatar} size={36} />

          <Spacer width={12} />

          <View style={styles.fullFlex}>
            <ThemedText style={[styles.username, isYou && styles.textPrimary]}>
              {isYou ? 'You' : item.user.username}
            </ThemedText>
          </View>
        </View>
      </UserProfileTouch>
    </View>
  );
});

export const CommunityFeeds = memo(() => {
  const { styles } = useStyles(stylesheet);

  const { data, isSuccess, fetchNextPage, hasNextPage, isFetchingNextPage } = useGetCommunityFeedQuery({
    limit: 20,
    page: 1,
  });
  const queryClient = useQueryClient();

  const communityFeedData = useMemo(() => {
    if (!data?.pages) return [];
    return data.pages.flatMap((page) => page.data || []);
  }, [data]);

  const renderItem = useCallback<ListRenderItem<ICommunityFeedItem>>(
    ({ item }) => <CommunityFeedCard item={item} />,
    []
  );

  const keyExtractor = useCallback((item: ICommunityFeedItem) => `${item.commentId}_${item.type}`, []);

  const renderSeparator = useCallback(
    () => (
      <View style={styles.separatorContainer}>
        <View style={styles.separator} />
      </View>
    ),
    [styles.separator, styles.separatorContainer]
  );

  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const handleRefresh = useCallback(async () => {
    await queryClient.resetQueries({ queryKey: queryKeys.podcasts.communityFeed() });
  }, [queryClient]);

  useEffect(() => {}, []);

  return (
    <TabFlashList
      data={communityFeedData}
      renderItem={renderItem}
      keyExtractor={keyExtractor}
      showsVerticalScrollIndicator={false}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      ItemSeparatorComponent={renderSeparator}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={5}
      ListEmptyComponent={
        isSuccess || communityFeedData.length > 0 ? (
          <View style={[styles.container, { justifyContent: 'center', alignItems: 'center' }]}>
            <Empty type='post' />
          </View>
        ) : null
      }
      onRefresh={handleRefresh}
      ListHeaderComponent={<Spacer height={24} />}
      ListFooterComponent={isFetchingNextPage ? <IconLoading /> : null}
    />
  );
});

const stylesheet = createStyleSheet((theme) => ({
  container: {
    backgroundColor: theme.colors.neutralBackground,
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    paddingBottom: 24,
    paddingHorizontal: 24,
  },
  separatorContainer: {
    paddingVertical: 24,
  },
  separator: {
    height: 1,
    backgroundColor: theme.colors.neutralDarkGrey,
  },
}));

const feedCardStylesheet = createStyleSheet((theme) => ({
  cardContainer: {},
  episodeHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  episodeCover: {
    width: 64,
    height: 64,
    borderRadius: 12,
  },
  episodeInfo: {
    flex: 1,
    marginLeft: 20,
    marginRight: 20,
  },
  episodeTitle: {
    color: theme.colors.neutralWhite,
    lineHeight: 20,
    marginBottom: 4,
  },
  showTitle: {
    color: theme.colors.neutralWhite,
    lineHeight: 18,
    marginBottom: 4,
    opacity: 0.56,
  },
  starsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  duration: {
    opacity: 0.56,
    color: theme.colors.neutralWhite,
    lineHeight: 18,
    textAlign: 'right',
  },
  reviewSection: {},
  reviewTitle: {
    color: theme.colors.neutralWhite,
  },
  reviewContent: {
    color: theme.colors.neutralWhite,
  },
  imagesSection: {
    marginHorizontal: -24,
  },
  imagesContainer: {
    paddingLeft: 24,
    gap: 6,
  },
  imageContainer: {
    width: 188,
    height: 188,
  },
  postImage: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
    backgroundColor: '#1F1E1E',
  },
  userSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userAvatar: {
    width: 36,
    height: 36,
    borderRadius: 1111,
  },
  username: {
    fontSize: 12,
    ...theme.fw500,
    color: theme.colors.neutralWhite,
  },
  expandButton: {
    color: theme.colors.neutralWhite,
    marginTop: 8,
    opacity: 0.7,
    lineHeight: 18,
  },
  textPrimary: {
    color: theme.colors.primary,
  },
  fullFlex: {
    flex: 1,
  },
}));
