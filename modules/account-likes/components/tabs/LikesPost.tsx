import { IUserProfileById, UserLikesPost } from '@/apis/user';
import { useGetInfiniteUserLikesPostsHistoryQuery } from '@/apis/user/queries';
import { IconLoading } from '@/components/IconLoading';
import { memo, useCallback } from 'react';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikedPostItem } from '../LikedPostItem';
import { Empty } from '@/components/Empty';
import { useIsYou } from '@/hooks/useIsYou';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { ListRenderItem } from '@shopify/flash-list';
import { Spacer } from '@/components/Spacer';

type Props = {
  userId: string;
  profile?: IUserProfileById;
};

export const LikesPost = memo(({ userId, profile }: Props) => {
  const { styles } = useStyles(stylesheet);

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const {
    data: likesPostData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetInfiniteUserLikesPostsHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesPost = likesPostData?.pages?.flatMap((page) => page.data) ?? [];

  const renderItem = useCallback<ListRenderItem<UserLikesPost>>(
    ({ item }) => <LikedPostItem likedPostItem={item} />,
    []
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: UserLikesPost) => `${item.parentId}-${item.commentId}`, []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderItemSeparator = useCallback(() => <View style={styles.line} />, [styles.line]);

  const isShowEmpty = !isFetching && likesPost.length === 0;

  return (
    <TabFlashList
      bounces={false}
      data={likesPost}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      keyExtractor={keyExtractor}
      renderItem={renderItem}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      ListHeaderComponent={<Spacer height={24} />}
      ListFooterComponent={isFetchingNextPage || isFetching ? renderSkeleton : null}
      ItemSeparatorComponent={renderItemSeparator}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='like'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} liked any posts`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    paddingTop: 24,
  },
  contentContainer: {
    paddingHorizontal: 24,
    paddingBottom: 24,
  },
  line: {
    height: 1,
    backgroundColor: theme.colors.whiteOpacity10,
    width: '100%',
    marginVertical: 24,
  },
}));
