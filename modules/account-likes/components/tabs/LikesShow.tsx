import { IUserProfileById, UserLikesPodcast } from '@/apis/user';
import { useGetInfiniteUserLikesPodcastsHistoryQuery } from '@/apis/user/queries';
import { Icons } from '@/assets/icons';
import { TabFlashList } from '@/components/collapsing-tabs/TabFlashList';
import { Empty } from '@/components/Empty';
import { IconLoading } from '@/components/IconLoading';
import { ThemedText } from '@/components/ThemedText';
import { useIsYou } from '@/hooks/useIsYou';
import { getItemSizeFlatList } from '@/utils/func';
import { showDetailDirect } from '@/utils/router-prefetch';
import { ListRenderItem } from '@shopify/flash-list';
import { useQueryClient } from '@tanstack/react-query';
import { memo, useCallback, useState } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { LikeShowItem } from '../LikeShowItem';
import { Spacer } from '@/components/Spacer';

const itemSize = getItemSizeFlatList(24, 3, 11);

type Props = {
  userId: string;
  profile?: IUserProfileById;
};

export const LikesShow = memo(({ userId, profile }: Props) => {
  const { styles } = useStyles(stylesheet);
  const queryClient = useQueryClient();
  const [listType, setListType] = useState<'list' | 'grid'>('list');

  const isYou = useIsYou({
    userId: userId ?? '',
  });

  const {
    data: likesPodcastsData,
    isFetching,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetInfiniteUserLikesPodcastsHistoryQuery({
    userId: userId ?? '',
    limit: 24,
  });

  const likesPodcasts = likesPodcastsData?.pages?.flatMap((page) => page.data) ?? [];

  const handleDirect = useCallback(
    (item: UserLikesPodcast) => {
      showDetailDirect(queryClient, item.podcastId.toString());
    },
    [queryClient]
  );

  const renderItem = useCallback<ListRenderItem<UserLikesPodcast>>(
    ({ item }) => <LikeShowItem item={item} listType={listType} itemSize={itemSize} onPress={handleDirect} />,
    [handleDirect, listType]
  );

  const handleLoadMore = useCallback(async () => {
    if (hasNextPage && !isFetchingNextPage) {
      await fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const keyExtractor = useCallback((item: UserLikesPodcast) => item.id.toString(), []);

  const renderSkeleton = useCallback(() => <IconLoading />, []);

  const renderHeader = useCallback(
    () => (
      <View style={styles.headerList}>
        <ThemedText type='defaultSemiBold'>Recent</ThemedText>

        {listType === 'list' ? (
          <TouchableOpacity onPress={() => setListType('grid')} activeOpacity={0.7}>
            <Icons.GridIcon size={24} color='#fff' />
          </TouchableOpacity>
        ) : (
          <TouchableOpacity onPress={() => setListType('list')} activeOpacity={0.7}>
            <Icons.ListIcon size={24} color='#fff' />
          </TouchableOpacity>
        )}
      </View>
    ),
    [styles, listType]
  );

  const renderSeparator = useCallback(() => <Spacer height={listType === 'list' ? 24 : 28} />, [listType]);

  const isShowEmpty = !isFetching && likesPodcasts.length === 0;

  return (
    <TabFlashList
      key={listType}
      extraData={listType}
      keyExtractor={keyExtractor}
      bounces={false}
      numColumns={listType === 'list' ? 1 : 3}
      data={likesPodcasts}
      style={styles.container}
      contentContainerStyle={styles.contentContainer}
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      ListHeaderComponent={renderHeader}
      renderItem={renderItem}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.4}
      estimatedItemSize={listType === 'list' ? 64 : itemSize}
      ItemSeparatorComponent={renderSeparator}
      ListFooterComponent={isFetchingNextPage || isFetching ? renderSkeleton : null}
      ListEmptyComponent={
        isShowEmpty ? (
          <Empty
            type='like'
            emptyText={`${isYou ? 'You' : profile?.username} ${isYou ? "haven't" : "hasn't"} liked any shows`}
          />
        ) : null
      }
    />
  );
});

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 24,
  },
  headerList: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 20,
  },
}));
