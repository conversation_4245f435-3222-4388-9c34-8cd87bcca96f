import { PostHistory } from '@/apis/user';
import { Icons } from '@/assets/icons';
import { RateStar } from '@/components/RateStar';
import { Show } from '@/components/Show';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import { ExpoImage } from '@/components/ui/Image';
import CommentImage from '@/modules/show-detail/components/CommentImage';
import { format } from 'date-fns';
import { router } from 'expo-router';
import { TouchableOpacity, View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';

type Props = {
  isEpisode: boolean;
  postInfo: PostHistory;
};

export const AccountPostItem = ({ isEpisode, postInfo }: Props) => {
  const { styles } = useStyles(stylesheet);
  const formatTime = format(new Date(postInfo.updatedAt), 'hh:mm a • MMM dd');

  const handleDirect = () => {
    if (isEpisode) {
      router.push({
        pathname: '/(app)/episode/[episodeId]/review/[postId]',
        params: {
          episodeId: postInfo.parentId,
          postId: postInfo.id,
          source: 'local',
        },
      });
    } else {
      router.push({
        pathname: '/(app)/podcast/[podcastId]/review/[postId]',
        params: {
          podcastId: postInfo.podcastId,
          postId: postInfo.id,
          source: 'local',
        },
      });
    }
  };

  return (
    <TouchableOpacity activeOpacity={0.7} onPress={handleDirect} style={styles.container}>
      <View style={styles.rowCenter}>
        <ExpoImage source={{ uri: postInfo.parentImageUrl }} style={styles.image} />

        <Spacer width={16} />

        <View style={styles.fullFlex}>
          <ThemedText type='tinyMedium'>{postInfo.parentTitle}</ThemedText>

          <Show when={isEpisode}>
            <ThemedText type='tinyMedium' style={styles.showName}>
              {postInfo.podcastTitle}
            </ThemedText>
          </Show>
        </View>
      </View>

      <Spacer height={16} />

      <View style={styles.rowCenter}>
        {postInfo.userRate ? <RateStar rating={postInfo.userRate} size={16} gap={4} /> : null}

        <Spacer width={12} />

        {postInfo?.hasLiked ? <Icons.HeartLikeFill size={16} /> : null}
      </View>

      <Spacer height={16} />

      <View>
        <ThemedText type='defaultMedium'>{postInfo.title}</ThemedText>

        <Spacer height={8} />

        <ThemedText type='smallNormal'>{postInfo.content}</ThemedText>
      </View>

      <Show when={postInfo.images.length > 0}>
        <>
          <Spacer height={16} />

          <CommentImage images={postInfo.images} />
        </>
      </Show>

      <Spacer height={16} />

      <ThemedText type='small' style={styles.time}>
        {formatTime}
      </ThemedText>
    </TouchableOpacity>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {},
  image: {
    borderRadius: 8,
    objectFit: 'cover',
    width: 64,
    height: 64,
  },
  rowCenter: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  showName: {
    color: theme.colors.whiteOpacity80,
  },
  fullFlex: {
    flex: 1,
  },
  imagesContainer: {
    gap: 8,
  },
  imagePost: {
    borderRadius: 8,
    objectFit: 'cover',
  },
  time: {
    color: theme.colors.whiteOpacity56,
  },
}));
