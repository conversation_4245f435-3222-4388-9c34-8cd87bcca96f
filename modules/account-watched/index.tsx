import { Header } from '@/components/ui/Header';
import { View } from 'react-native';
import { createStyleSheet, useStyles } from 'react-native-unistyles';
import { useLocalSearchParams } from 'expo-router';
import { useIsYou } from '@/hooks/useIsYou';
import { useGetUserProfileByIdQuery } from '@/apis/user/queries';
import { WatchedAll } from './components/WatchedAll';
import { TabContainer } from '@/components/collapsing-tabs';
import { TabItem } from '@/components/collapsing-tabs/TabItem';

export const AccountWatched = () => {
  const { styles } = useStyles(stylesheet);
  const { userId } = useLocalSearchParams<{ userId: string }>();

  const { data: userProfileById, isPending: isPendingUserProfile } = useGetUserProfileByIdQuery(userId ?? '', {
    enabled: !!userId,
  });

  const isYou = useIsYou({
    userId: userProfileById?.id?.toString() ?? '',
  });

  return (
    <View style={styles.container}>
      <View style={styles.containerPadding}>
        <Header
          title={isPendingUserProfile ? '' : isYou ? 'My Watched' : `${userProfileById?.username} Watched`}
          isBack
        />
      </View>

      <TabContainer containerStyle={styles.tabContainer}>
        <TabItem tabName='All' label={'All'}>
          <WatchedAll userId={userId} tab={'All'} profile={userProfileById} />
        </TabItem>

        <TabItem tabName='Shows' label={'Shows'}>
          <WatchedAll userId={userId} tab={'Shows'} profile={userProfileById} />
        </TabItem>

        <TabItem tabName='Episodes' label={'Episodes'}>
          <WatchedAll userId={userId} tab={'Episodes'} profile={userProfileById} />
        </TabItem>
      </TabContainer>
    </View>
  );
};

const stylesheet = createStyleSheet((theme, rt) => ({
  container: {
    flex: 1,
    backgroundColor: theme.colors.neutralBackground,
    paddingTop: rt.insets.top,
    paddingBottom: rt.insets.bottom,
  },
  containerPadding: {
    paddingHorizontal: 24,
  },
  tabContainer: {
    flex: 1,
    overflow: 'hidden',
  },
  headerContainer: {
    backgroundColor: 'transparent',
  },
}));
